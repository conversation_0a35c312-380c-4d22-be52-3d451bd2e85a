# Video Converter API

A standalone video converter API server built with NestJS that provides video conversion, thumbnail generation, and metadata extraction capabilities.

## Features

- **Video Conversion**: Convert videos to MP4 format with H.264 encoding
- **Real-time Progress**: WebSocket support for real-time conversion progress updates
- **Queue Processing**: Background video processing using Redis queues
- **Thumbnail Generation**: Create video thumbnails
- **Metadata Extraction**: Extract video metadata using FFprobe
- **HTML5 Support Check**: Check if video supports HTML5 playback
- **File Upload**: Upload videos for processing

## Installation

1. Install dependencies:
```bash
npm install
# or
yarn install
```

2. Copy environment configuration:
```bash
cp .env.example .env
```

3. Configure environment variables in `.env` file

4. Start the server:
```bash
# Development
npm run start:dev

# Production
npm run build
npm run start:prod
```

## API Endpoints

### Video Conversion

#### POST `/video-converter/convert`
Convert a video file to MP4 format.

**Request Body:**
```json
{
  "filePath": "/path/to/video.avi",
  "toPath": "/path/to/output.mp4",
  "size": "1280x720",
  "socketId": "socket-id-for-progress"
}
```

#### POST `/video-converter/convert/queue`
Queue a video for background conversion.

**Request Body:**
```json
{
  "filePath": "/path/to/video.avi",
  "toPath": "/path/to/output.mp4",
  "size": "1280x720",
  "socketId": "socket-id-for-progress"
}
```

### File Upload

#### POST `/video-converter/upload`
Upload a video file for processing.

**Form Data:**
- `video`: Video file

### Metadata

#### POST `/video-converter/metadata`
Get video metadata.

**Request Body:**
```json
{
  "filePath": "/path/to/video.mp4"
}
```

### Thumbnails

#### POST `/video-converter/thumbnails`
Generate video thumbnails.

**Request Body:**
```json
{
  "filePath": "/path/to/video.mp4",
  "toFolder": "/path/to/thumbnails",
  "count": 3,
  "size": "480x?"
}
```

### HTML5 Support

#### GET `/video-converter/check-html5/:filePath`
Check if video supports HTML5 playback.

## WebSocket Events

Connect to the WebSocket server to receive real-time updates:

### Events Emitted by Server:

- `video_conversion_progress`: Progress updates during conversion
- `video_conversion_complete`: Conversion completed successfully
- `video_conversion_error`: Conversion failed

### Events to Send to Server:

- `join-room`: Join a specific room for targeted updates

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `HTTP_PORT` | Server port | `8081` |
| `NODE_ENV` | Environment | `development` |
| `CORS_ORIGIN` | CORS origin | `*` |
| `REDIS_HOST` | Redis host | `127.0.0.1` |
| `REDIS_PORT` | Redis port | `6379` |
| `REDIS_DB` | Redis database | `0` |
| `REDIS_PASSWORD` | Redis password | `` |
| `REDIS_PREFIX` | Redis key prefix | `video_converter` |
| `UPLOAD_DIR` | Upload directory | `./uploads` |
| `TEMP_DIR` | Temporary directory | `./temp` |

## Dependencies

- **FFmpeg**: Required for video processing
- **Redis**: Required for queue processing

## Development

```bash
# Start in development mode
npm run start:dev

# Run tests
npm run test

# Build for production
npm run build
```

## API Documentation

When running in development mode, Swagger documentation is available at:
`http://localhost:8081/api-docs`
