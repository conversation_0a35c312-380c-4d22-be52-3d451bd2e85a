const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

const API_BASE_URL = 'http://localhost:8081';

async function testHealthCheck() {
  try {
    const response = await axios.get(`${API_BASE_URL}/video-converter/health`);
    console.log('Health Check:', response.data);
    return true;
  } catch (error) {
    console.error('Health Check Failed:', error.message);
    return false;
  }
}

async function testVideoMetadata(filePath) {
  try {
    const response = await axios.post(`${API_BASE_URL}/video-converter/metadata`, {
      filePath: filePath
    });
    console.log('Video Metadata:', response.data);
    return true;
  } catch (error) {
    console.error('Metadata Test Failed:', error.response?.data || error.message);
    return false;
  }
}

async function testVideoConversion(filePath, outputPath) {
  try {
    const response = await axios.post(`${API_BASE_URL}/video-converter/convert`, {
      filePath: filePath,
      toPath: outputPath,
      size: '1280x720'
    });
    console.log('Video Conversion:', response.data);
    return true;
  } catch (error) {
    console.error('Conversion Test Failed:', error.response?.data || error.message);
    return false;
  }
}

async function testFileUpload(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log('Test file not found, skipping upload test');
      return false;
    }

    const form = new FormData();
    form.append('video', fs.createReadStream(filePath));

    const response = await axios.post(`${API_BASE_URL}/video-converter/upload`, form, {
      headers: {
        ...form.getHeaders(),
      },
    });
    console.log('File Upload:', response.data);
    return true;
  } catch (error) {
    console.error('Upload Test Failed:', error.response?.data || error.message);
    return false;
  }
}

async function runTests() {
  console.log('Starting Video Converter API Tests...\n');

  // Test health check
  console.log('1. Testing Health Check...');
  await testHealthCheck();
  console.log('');

  // You can add a test video file path here
  const testVideoPath = './test-video.mp4';
  
  if (fs.existsSync(testVideoPath)) {
    console.log('2. Testing Video Metadata...');
    await testVideoMetadata(testVideoPath);
    console.log('');

    console.log('3. Testing File Upload...');
    await testFileUpload(testVideoPath);
    console.log('');

    console.log('4. Testing Video Conversion...');
    await testVideoConversion(testVideoPath, './output.mp4');
    console.log('');
  } else {
    console.log('Test video file not found. Create a test-video.mp4 file to run full tests.');
  }

  console.log('Tests completed!');
}

if (require.main === module) {
  runTests();
}

module.exports = {
  testHealthCheck,
  testVideoMetadata,
  testVideoConversion,
  testFileUpload
};
